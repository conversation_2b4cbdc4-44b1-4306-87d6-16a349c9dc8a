import React, { useState } from "react";
import fullStar from "@/assets/svgs/fullStar.svg";
import lessonIcon from "@/assets/svgs/lessons.svg";
import greenCheck from "@/assets/svgs/greencheck.svg";
import { Button } from "@/components/button/button";
import MessageCard from "../message/MessageCard";
import { useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";

const TutorCard = ({ tutor }) => {
  const user = useSelector((state) => state?.app?.userInfo?.user);
  const [imageError, setImageError] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [expanded, setExpanded] = useState(false);
  const MAX_LENGTH = 150;
  const navigate = useNavigate();

  const toggleExpanded = () => setExpanded((prev) => !prev);

  const aboutMeText = tutor?.aboutMe || ""; // fallback to empty string if null
  const isLong = aboutMeText.length > MAX_LENGTH;
  const displayText =
    expanded || !isLong
      ? aboutMeText
      : `${aboutMeText.slice(0, MAX_LENGTH)}...`;

  const openModal = () => {
    setIsModalOpen(true);
    console.log("button clicked");
  };
  console.log("tutor review", tutor.reviewStats);

  const closeModal = () => setIsModalOpen(false);

  const bookFreeTrial = (e, tutor) => {
    e.stopPropagation();

    if (!user) {
      navigate("/signin");
      return;
    }

    navigate(`/student/my-lessons/tutors/${tutor._id}`);
  };

  const handleImageError = () => {
    setImageError(true);
  };

  return (
    <div className="border border-[#E8E8E8] rounded-xl p-3 flex gap-5 mb-5">
      <div className="w-full">
        {/* Desktop view */}
        <div className="md:flex hidden gap-5 items-stretch">
          {!imageError ? (
            <img
              src={tutor?.image}
              alt={`${tutor?.fullname || "Tutor"} profile`}
              className="w-full max-w-[290px] max-h-[200px] object-center rounded-xl"
              onError={handleImageError}
              loading="lazy"
            />
          ) : (
            <div className="w-full max-w-[290px] max-h-[200px] bg-gray-200 rounded-xl flex items-center justify-center">
              <span className="text-gray-500 text-lg font-medium">
                {tutor?.fullname?.charAt(0) || "T"}
              </span>
            </div>
          )}

          <div className="flex w-full gap-5">
            <div className="flex-1">
              <div className="flex gap-2 items-center mb-3">
                <h3 className="text-lg xl:text-[26px] font-bold text-gray-900">
                  {tutor?.fullname}
                </h3>

                {tutor?.approvalStatus === "approved" && (
                  <img
                    src={greenCheck}
                    alt="Verified tutor"
                    className="w-5 h-5 flex-shrink-0"
                  />
                )}
              </div>

              {/* Subjects */}
              {tutor?.teachingSubjects?.length > 0 && (
                <div className="mb-4">
                  {tutor.teachingSubjects.map((subject) => (
                    <div key={subject.id} className="flex flex-wrap mb-2">
                      {subject.qualities?.map((q, index) => (
                        <span
                          key={index}
                          className="border border-[#E8E8E8] px-3 py-1 rounded-full m-1 text-sm bg-gray-50 hover:bg-gray-100 transition-colors"
                        >
                          {q}
                        </span>
                      ))}
                    </div>
                  ))}
                </div>
              )}

              <div className="flex items-center gap-2 mb-3">
                <img
                  src={lessonIcon}
                  alt="Lessons completed"
                  className="w-4 h-4"
                />
                <span className="text-[#4B5563] text-sm">
                  {tutor?.totalLessons || 0} Lesson
                  {(tutor?.totalLessons || 0) !== 1 ? "s" : ""}
                </span>
              </div>

              {/* Languages */}
              {tutor?.languages?.length > 0 && (
                <div className="space-y-1">
                  {tutor.languages.map((lang) => (
                    <div key={lang.id} className="flex items-center gap-1">
                      <span className="text-[#4B5563] text-sm">
                        {lang.name}:
                      </span>
                      <span className="text-gray-900 text-sm font-medium">
                        {lang.level}
                      </span>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div className="flex gap-6 lg:min-w-[45%] justify-end items-start">
              <div className="flex flex-col items-center">
                {tutor?.reviewStats?.totalReview > 0 ? (
                  <div className="text-center">
                    <div className="flex items-center gap-1 mb-1">
                      <img src={fullStar} alt="Rating" className="w-5 h-5" />
                      <span className="font-semibold text-lg">
                        {Number(tutor.reviewStats.avgRating).toFixed(1)}
                      </span>
                    </div>
                    <p className="text-[#4B5563] text-sm">
                      ({tutor.reviewStats.totalReview} review
                      {tutor.reviewStats.totalReview > 1 ? "s" : ""})
                    </p>
                  </div>
                ) : (
                  <div className="text-center">
                    <div className="flex items-center gap-1 mb-1">
                      <img
                        src={fullStar}
                        alt="No ratings yet"
                        className="w-5 h-5 opacity-50"
                      />
                      <span className="text-gray-400 text-lg">New</span>
                    </div>
                    <p className="text-[#4B5563] text-sm">No reviews yet</p>
                  </div>
                )}
              </div>

              <div className="text-right">
                <h2 className="font-bold text-2xl text-gray-900">
                  $ {tutor.basePrice}
                </h2>
                <p className="text-[#4B5563] text-sm">per lesson</p>
              </div>
            </div>
          </div>
        </div>

        {/* Mobile View */}
        <div className="md:hidden">
          <div className="flex gap-3 mb-4">
            {!imageError ? (
              <img
                src={tutor?.image}
                alt={`${tutor?.firstname} ${tutor?.lastname}`}
                className="w-[100px] h-[120px] object-cover rounded-lg "
                onError={handleImageError}
                loading="lazy"
              />
            ) : (
              <div className="w-[100px] h-[120px] bg-gray-200 rounded-lg flex items-center justify-center flex-shrink-0">
                <span className="text-gray-500 text-xl font-medium">
                  {tutor?.firstname?.charAt(0) || "T"}
                </span>
              </div>
            )}

            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-2">
                <h3 className="text-lg font-bold text-gray-900 truncate">
                  {tutor?.firstname} {tutor?.lastname}
                </h3>
                {tutor?.approvalStatus === "approved" && (
                  <img
                    src={greenCheck}
                    alt="Verified"
                    className="w-4 h-4 flex-shrink-0"
                  />
                )}
              </div>

              <div className="flex justify-between items-start gap-3">
                <div className="flex flex-col">
                  {tutor?.reviewStats?.totalReview > 0 ? (
                    <div>
                      <div className="flex items-center gap-1 mb-1">
                        <img src={fullStar} alt="Rating" className="w-4 h-4" />
                        <span className="text-sm font-semibold">
                          {Number(tutor.reviewStats.avgRating).toFixed(1)}
                        </span>
                      </div>
                      <p className="text-[#4B5563] text-xs">
                        ({tutor.reviewStats.totalReview} review
                        {tutor.reviewStats.totalReview > 1 ? "s" : ""})
                      </p>
                    </div>
                  ) : (
                    <div>
                      <div className="flex items-center gap-1 mb-1">
                        <img
                          src={fullStar}
                          alt="No ratings"
                          className="w-4 h-4 opacity-50"
                        />
                        <span className="text-gray-400 text-sm">New</span>
                      </div>
                    </div>
                  )}
                </div>

                <div className="text-right">
                  <h2 className="font-bold text-lg text-gray-900">
                    $ {tutor?.basePrice}
                  </h2>
                  <p className="text-[#4B5563] text-xs">50-min lesson</p>
                </div>
              </div>
            </div>
          </div>

          {/* Subjects for mobile */}
          {tutor?.teachingSubjects?.length > 0 && (
            <div className="flex flex-wrap mb-3">
              {tutor.teachingSubjects.map((subject) => (
                <div key={subject.id} className="flex flex-wrap">
                  {subject.qualities?.map((q, index) => (
                    <span
                      key={index}
                      className="border border-[#E8E8E8] px-2 py-1 rounded-full m-1 text-xs bg-gray-50"
                    >
                      {q}
                    </span>
                  ))}
                </div>
              ))}
            </div>
          )}

          <div className="flex items-center gap-2 mb-3">
            <img src={lessonIcon} alt="Lessons" className="w-4 h-4" />
            <span className="text-[#4B5563] text-sm">
              {tutor?.totalLessons || 0} Lesson
              {(tutor?.totalLessons || 0) !== 1 ? "s" : ""}
            </span>
          </div>

          {tutor?.languages?.length > 0 && (
            <div className="flex flex-wrap gap-x-4 gap-y-1 mb-3">
              {tutor.languages.map((lang) => (
                <div key={lang.id} className="flex items-center gap-1">
                  <span className="text-[#4B5563] text-sm">{lang.name}:</span>
                  <span className="text-gray-900 text-sm font-medium">
                    {lang.level}
                  </span>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* About Me */}
        <div>
          <h3 className="sm:text-xl text-secondary font-bold mb-3 mt-5">
            About
          </h3>
          <p className="text-[#4B5563] sm:text-lg mb-3">{displayText}</p>
          {isLong && (
            <a
              href="#"
              onClick={(e) => {
                e.preventDefault();
                toggleExpanded();
              }}
              className="underline block mb-5 text-primary font-bold"
            >
              {expanded ? "Read less" : "Read more"}
            </a>
          )}
          <Button
            onClick={(e) => bookFreeTrial(e, tutor)}
            className="w-full lg:hidden"
          >
            Book free lesson
          </Button>
          <button
            onClick={openModal}
            className="w-full mt-2 border-primary border rounded-md py-2 fill-none lg:hidden"
          >
            Send Message
          </button>
        </div>
      </div>

      {/* Video and CTA buttons (Desktop only) */}
      <div className="lg:max-w-[240px] grow hidden lg:flex flex-col gap-4">
        <video
          src={tutor?.introVideo}
          controls
          className="w-full h-full object-cover rounded-xl"
        />

        <Button
          onClick={(e) => bookFreeTrial(e, tutor)}
          className="w-full text-lg"
        >
          Book free lesson
        </Button>
        <button
          onClick={openModal}
          className="w-full text-lg border rounded-md px-2 py-1 text-primary border-primary"
        >
          Send Message
        </button>
      </div>

      {/* Modal (shared) */}
      {isModalOpen && (
        <div className="inset-0 fixed z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="sm:w-[669px]">
            <MessageCard tutor={tutor} onClose={closeModal} />
          </div>
        </div>
      )}
    </div>
  );
};

export default TutorCard;
