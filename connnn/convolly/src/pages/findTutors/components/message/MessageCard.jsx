import { X } from "lucide-react";
import React, { useMemo } from "react";
import img from "../../../../assets/images/tutor1.png";
import { Button } from "@/components/button/button";
import { useForm } from "react-hook-form";
import { useSelector } from "react-redux";
import { useChatHook } from "@/hooks/useChatProvider";
import { toast } from "react-toastify";
import { Textarea } from "@/components/inputs/textInput";

const MessageCard = ({ onClose, tutor }) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm();

  const { message } = watch();

  const { sendChatMessage, loading } = useChatHook(
    useMemo(
      () => ({
        receiver: tutor,
        onMessageSaved: () => {
          onClose();
          toast.success("Message sent successfully.");
        },
      }),
      [tutor, onClose]
    )
  );

  // Get current user from Redux
  const currentUser = useSelector((state) => state?.app?.userInfo?.user);

  const tutorName = tutor
    ? `${tutor.firstname || tutor.firstName || ""} ${
        tutor.lastname || tutor.lastName || ""
      }`.trim()
    : "Tutor";

  const studentName = currentUser
    ? `${currentUser.firstname || currentUser.firstName || ""}`
    : "Student";

  const onSubmit = ({ message }) => {
    sendChatMessage({
      lookupConversation: true,
      chat: {
        message,
      },
    });
  };

  return (
    <div className="sm:w-[669px] w-sm mx-auto p-3 sm:p-6 bg-white rounded-lg shadow-lg">
      <div className="flex gap-[145px] pt-4 justify-between">
        <p className="text-2xl font-bold text-[#1A1A40]">Send Message</p>
        <button onClick={onClose} disabled={loading}>
          <X />
        </button>
      </div>
      <div className="px-1 sm:px-2 min-h-[552px]">
        <div className="gap-3 ">
          <div className="flex justify-start my-4">
            <div className="">
              <img
                src={tutor?.image || tutor?.profilePicture || img}
                alt="tutor logo"
                className="w-24 h-24 rounded-full object-cover"
              />
            </div>
            <p className="text-lg sm:text-[26px] px-2 text-[#1A1A40]">
              {tutorName}
            </p>
          </div>
          <div className="pb-4 text-[#4B5563] sm:font-medium text-xs sm:text-[18px]">
            <div className="">
              <p className="mb-1">Hi {studentName}</p>
              <p className=" pb-4  font-medium ">
                I hope everything is going well for you. Thanks for looking at
                my profile
              </p>
            </div>

            <p className="mt-4 space-y-2">
              Introduce yourself to the teacher, what are your learning goals?
              How can I help you in the best way?
            </p>
          </div>
        </div>
        <div className="mt-2">
          <p className="text-md sm:text-[22px] font-bold text-[#1A1A40]">
            Send a personal message to the teacher
          </p>
          <div className="text-xs sm:text-lg">
            <div className="flex flex-col space-y-2">
              <Textarea
                {...register("message", { required: "Message is required" })}
                placeholder="Your Message"
                className="border border-[#E8E8E8] mt-2 rounded-md w-full min-h-[200px] p-3 focus:outline-none"
                disabled={loading}
                onEnter={() => onSubmit({ message })}
              />
              {errors.message && (
                <p className="text-red-500 text-sm">{errors.message.message}</p>
              )}
            </div>
          </div>
        </div>
        <div className="mt-6">
          <Button
            onClick={handleSubmit(onSubmit)}
            className="bg-primary border w-full text-white rounded-md hover:bg-primary-dark transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            loading={loading}
            disabled={!message}
          >
            {"Send Message"}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default MessageCard;
