import React, { useState } from "react";
import Navbar from "../../components/navbar/navbar";
import { CustomSelect } from "../../components/select/select";
import MultiSelect from "../../components/select/multiSelect";
import { ChevronDownIcon } from "lucide-react";
import TutorCard from "./components/tutorCard/tutorCard";
import PriceDropdown from "./components/priceDropdown/priceDropdown";
import AvailabilityDropdown from "./components/availabilityDropdown/availabilityDropdown";
import MobileFilter from "./components/mobile/MobileFilter";
import MobileSearch from "./components/mobile/MobileSearch";
import searchImg from "../../assets/svgs/findtutor/search-02.svg";
import filterImg from "../../assets/svgs/findtutor/filter-horizontal.svg";
import worksImg from "../../assets/svgs/findtutor/work.svg";
import message from "../../assets/svgs/findtutor/message.svg";
import chartUp from "../../assets/svgs/findtutor/chart-up.svg";
import calender from "../../assets/svgs/findtutor/calendar.svg";
import saleTag from "../../assets/svgs/findtutor/sale-tag.svg";
import selectCountry from "../../assets/svgs/findtutor/select-country.svg";
import starAward from "../../assets/svgs/findtutor/star-award.svg";
import ApleIntelligence from "../../assets/svgs/findtutor/apple-intelligence.svg";
import { useGetTutorsQuery } from "@/redux/slices/student/findTutorApiSlice";
import Loader from "@/components/loader/loader";
import { useForm } from "react-hook-form";
import {
  industries,
  specialities,
  qualities,
  languageLevels,
  countries,
  alsoSpeaks,
  availabilityTimes,
  daysOfWeek,
} from "./components/tutorCard/utils";
import useChatProvider from "@/hooks/useChatProvider";

const FindTutors = () => {
  const [showFilter, setShowFilter] = React.useState(false);
  const [showSearch, setShowSearch] = React.useState(false);
  const [searchInput, setSearchInput] = useState("");
  const [filters, setFilters] = React.useState({});

  const { socketIsConnected } = useChatProvider();

  const { control, reset } = useForm();

  // Handle input change for search
  const handleInputChange = (e) => {
    setSearchInput(e.target.value);
  };
  // Fetch tutors
  const {
    data: tutorsResponse,
    isLoading,
    refetch,
    error,
  } = useGetTutorsQuery();

  console.log("Original tutors data:", tutorsResponse?.data);
  const getHour = (timeStr) => parseInt(timeStr.split(":")[0]);

  // main filter function
  const filteredTutors = React.useMemo(() => {
    if (
      !tutorsResponse ||
      !tutorsResponse.success ||
      !Array.isArray(tutorsResponse?.data)
    ) {
      return [];
    }

    return tutorsResponse.data.filter((tutor) => {
      // Only show approved tutors (additional frontend safety check)
      const isApproved = tutor?.approvalStatus === "approved";

      // Country filter (multi-select)
      const matchesCountry = filters.country
        ? filters.country.some((filterCountry) =>
            tutor?.countryOfBirth
              ?.toLowerCase()
              .includes(filterCountry.toLowerCase())
          )
        : true;

      // Industry filter
      const matchesIndustry = filters.industry
        ? tutor?.industry?.toLowerCase() === filters.industry.toLowerCase()
        : true;

      // Availability filter
      const matchesAvailability = filters.availability
        ? tutor?.timeAvailable?.some((slot) => {
            const fromHour = getHour(slot.from);
            if (filters.availability === "Morning")
              return fromHour >= 6 && fromHour < 12;
            if (filters.availability === "Afternoon")
              return fromHour >= 12 && fromHour < 17;
            if (filters.availability === "Evening")
              return fromHour >= 17 && fromHour < 22;
            return false;
          })
        : true;

      // Price range filter
      const matchesPrice =
        (!filters.minPrice || tutor.basePrice >= filters.minPrice) &&
        (!filters.maxPrice || tutor.basePrice <= filters.maxPrice);

      // Language filter
      const matchesLanguage = filters.language
        ? tutor?.languages?.some(
            (lang) =>
              lang?.name?.toLowerCase() === filters.language.toLowerCase()
          )
        : true;

      // Speciality filter
      const matchesSpeciality = filters.speciality
        ? tutor?.teachingSubjects?.some((subject) =>
            subject?.specialities?.some(
              (s) => s.toLowerCase() === filters.speciality.toLowerCase()
            )
          )
        : true;

      // Qualities filter (multi-select)
      const matchesQualities = filters.qualities
        ? tutor?.teachingSubjects?.some((subject) =>
            subject?.qualities?.some((q) =>
              filters.qualities.includes(q.toLowerCase())
            )
          )
        : true;

      // Experience level filter
      const matchesExperienceLevel = filters.experienceLevel
        ? tutor?.languages?.some(
            (lang) =>
              lang?.level?.toLowerCase() ===
              filters.experienceLevel.toLowerCase()
          )
        : true;

      // Search by name
      const matchesSearch = searchInput
        ? tutor?.firstname?.toLowerCase().includes(searchInput.toLowerCase())
        : true;

      return (
        isApproved &&
        matchesSearch &&
        matchesLanguage &&
        matchesSpeciality &&
        matchesCountry &&
        matchesQualities &&
        matchesExperienceLevel &&
        matchesIndustry &&
        matchesAvailability &&
        matchesPrice
      );
    });
  }, [tutorsResponse, searchInput, filters]);
  // onchange function to
  const handleFilterChange = (key, value) => {
    setFilters((prevFilters) => {
      // If value is empty/falsy, remove the filter
      if (!value || (Array.isArray(value) && value.length === 0)) {
        const updated = { ...prevFilters };
        delete updated[key];
        return updated;
      }

      // For multi-select values (arrays), store them as arrays
      if (Array.isArray(value)) {
        return {
          ...prevFilters,
          [key]: value.map((v) => v.toLowerCase()),
        };
      }

      // For single values, store as strings
      return {
        ...prevFilters,
        [key]: value.toLowerCase(),
      };
    });
  };

  const clearAllFilters = () => {
    setFilters({});
    setSearchInput("");
    reset({
      industry: null,
      country: [],
      speaks: null,
      speciality: null,
      qualities: [],
      level: null,
      // Add other form fields here
    });
  };
  const applyFilters = () => {
    console.log("Filters applied:", filters);
    setShowFilter(false); // close mobile filter modal
  };

  // Render loading state
  if (isLoading || !socketIsConnected) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader />
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        Error loading tutors: {error.message}
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <Navbar />
      {/* Main content container */}
      <div className="mx-auto px-4 sm:px-8 pt-4">
        {/* Mobile filters */}
        <div className="sm:hidden">
          <div className="flex justify-between mb-4">
            <p className="font-bold text-xl mb-3">Filter tutors by</p>

            <button onClick={clearAllFilters} className="  font-semibold">
              Clear
            </button>
          </div>
          <div className="flex border-[#E8E8E8] items-center h-[50px] text-[#A4A4A4] gap-[12px]">
            <div className="flex-1 w-[258px] border-[1px] flex rounded-md p-[8px] gap-2">
              <img
                src={worksImg}
                alt="works img"
                width={24}
                height={24}
                className="pb-2"
              />
              <CustomSelect
                placeholder="Industry"
                label=""
                className="border-none items-center h-[27px]"
                options={industries.map((industry) => ({
                  value: industry.toLowerCase(),
                  label: industry,
                }))}
                name="industry"
                control={control}
                isRequired={false}
                onChange={(selected) =>
                  handleFilterChange("industry", selected?.value)
                }
              />
            </div>
            <button
              onClick={() => setShowFilter(true)}
              className="flex-1 sm:flex-none w-[117px] flex items-center justify-between rounded-md p-[10px] border-[1px] gap-2"
            >
              {/* Left side: image + label */}
              <div className="flex items-center gap-2">
                <img
                  src={filterImg}
                  alt="filterImg img"
                  width={24}
                  height={24}
                  className="pb-1"
                />
                <span>Filter</span>
              </div>
            </button>

            <button
              onClick={() => setShowSearch(true)}
              className="flex-none w-[50px] sm:w-[100px] flex justify-center rounded-md p-[10px] border-[1px] h-[50px]"
            >
              <img
                src={searchImg}
                alt="works img"
                width={24}
                height={24}
                className=""
              />
            </button>
          </div>

          {/* Mobile filter overlay */}

          {showFilter && (
            <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-start">
              <div className="min-h-full bg-white w-full p-4 animate-slideFromTop">
                <MobileFilter
                  onClose={() => setShowFilter(false)}
                  currentFilters={filters}
                  filters={filters}
                  handleFilterChange={handleFilterChange}
                  onClearFilters={clearAllFilters}
                  searchInput={searchInput}
                  onSearch={(value) => setSearchInput(value)}
                  onApplyFilters={applyFilters}
                />
              </div>
            </div>
          )}

          {/* Mobile search overlay */}
          {showSearch && (
            <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-start">
              <div className="min-h-full bg-white w-full p-4 animate-slideFromTop">
                <MobileSearch
                  onClose={() => setShowSearch(false)}
                  searchQuery={searchInput}
                  onInputChange={handleInputChange}
                  onSearch={(value) => setSearchInput(value)}
                />
              </div>
            </div>
          )}

          {/* Mobile results count */}
          <br />
          <div className="flex items-center justify-between my-5">
            <h4 className="text-xl text-secondary font-bold">
              {filteredTutors.length} tutor(s) found
            </h4>
            <div className="flex items-center gap-[3px]">
              <span className="text-[#A4A4A4] text-base">Sort by</span>
              <ChevronDownIcon className="w-5 h-5 text-[#A4A4A4]" />
            </div>
          </div>
        </div>

        {/* Desktop filters */}
        <div className="hidden sm:block">
          <p className="font-bold text-xl mb-3">Filter tutors by</p>

          {/* Single responsive grid that adjusts to screen size */}
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 mb-6">
            {/* Industry */}
            <div className="flex items-center h-[50px] border-[1px] focus:ring-2 focus:ring-primary p-[10px] hover:border-black border-gray-300 rounded-md">
              <div>
                <img src={worksImg} alt="industry icon" />
              </div>
              <CustomSelect
                placeholder="Industry"
                label=""
                className="border-none"
                options={industries.map((industry) => ({
                  value: industry.toLowerCase(),
                  label: industry,
                }))}
                name="industry"
                control={control}
                isRequired={false}
                value={filters.industry}
                onChange={(selected) =>
                  handleFilterChange("industry", selected?.value)
                }
              />
            </div>

            {/* Country */}
            <div className="flex items-center gap-2 h-[50px] border-[1px] focus:ring-2 focus:ring-primary p-[10px] hover:border-black border-gray-300 rounded-md">
              <div>
                <img
                  src={selectCountry}
                  alt="Languages Icon"
                  height={24}
                  width={24}
                />
              </div>
              <MultiSelect
                options={countries.map((country) => ({
                  label: country,
                  value: country.toLowerCase(),
                }))}
                placeholder="Select country"
                buttonClassName="border-none"
                control={control}
                name="country"
                value={filters.country || []}
                onChange={(selected) =>
                  handleFilterChange(
                    "country",
                    selected?.map((item) => item.value)
                  )
                }
              />
            </div>

            {/* Price */}
            <div className="flex items-center gap-2 h-[50px] border-[1px] focus:ring-2 focus:ring-primary p-[10px] hover:border-black border-gray-300 rounded-md">
              <div>
                <img src={saleTag} alt="price icon" height={24} width={24} />
              </div>
              <PriceDropdown
                className="border-none"
                onChange={({ min, max }) => {
                  setFilters((prev) => ({
                    ...prev,
                    minPrice: min,
                    maxPrice: max,
                  }));
                }}
              />
            </div>

            {/* Availability */}
            <div className="flex items-center h-[50px] border-[1px] focus:ring-2 focus:ring-primary p-[10px] hover:border-black border-gray-300 rounded-md">
              <div>
                <img src={calender} alt="Availability Time icon" />
              </div>
              <AvailabilityDropdown
                times={availabilityTimes}
                days={daysOfWeek}
                className="border-none"
                value={filters.availability}
                onChange={(selected) =>
                  handleFilterChange("availability", selected?.value)
                }
              />
            </div>

            {/* Also Speaks */}
            <div className="flex items-center h-[50px] border-[1px] focus:ring-2 focus:ring-primary p-[10px] hover:border-black border-gray-300 rounded-md">
              <div>
                <img src={message} alt="other language icons" />
              </div>
              <CustomSelect
                placeholder="Also Speaks"
                label=""
                className="border-none"
                options={alsoSpeaks.map((lang) => ({
                  value: lang.toLowerCase(),
                  label: lang,
                }))}
                name="speaks"
                control={control}
                isRequired={false}
                value={filters.language}
                onChange={(selected) =>
                  handleFilterChange("language", selected?.value)
                }
              />
            </div>

            {/* Specialities */}
            <div className="flex items-center h-[50px] border-[1px] focus:ring-2 focus:ring-primary p-[10px] hover:border-black border-gray-300 rounded-md">
              <div>
                <img src={ApleIntelligence} alt="specialities icon" />
              </div>
              <CustomSelect
                placeholder="Specialities"
                label=""
                className="border-none"
                options={specialities.map((spec) => ({
                  value: spec.toLowerCase().replace(" ", "_"),
                  label: spec,
                }))}
                name="speciality"
                control={control}
                isRequired={false}
                value={filters.speciality}
                onChange={(selected) =>
                  handleFilterChange("speciality", selected?.value)
                }
              />
            </div>

            {/* Qualities */}
            <div className="flex items-center h-[50px] border-[1px] focus:ring-2 focus:ring-primary p-[10px] hover:border-black border-gray-300 rounded-md">
              <div>
                <img src={starAward} alt="Qualitites icon" />
              </div>
              <MultiSelect
                options={qualities.map((quality) => ({
                  label: quality,
                  value: quality.toLowerCase().replace(" ", "_"),
                }))}
                placeholder="Select qualities"
                buttonClassName="border-none"
                control={control}
                name="qualities"
                onChange={(selected) =>
                  handleFilterChange(
                    "qualitiies",
                    selected?.map((item) => item.value)
                  )
                }
              />
            </div>

            {/* Language Level */}
            <div className="flex items-center h-[50px] border-[1px] focus:ring-2 focus:ring-primary p-[10px] hover:border-black border-gray-300 rounded-md">
              <div>
                <img src={chartUp} alt="language level " />
              </div>
              <CustomSelect
                placeholder="Language Level"
                className="border-none"
                label=""
                options={languageLevels.map((level) => ({
                  value: level.toLowerCase(),
                  label: level,
                }))}
                name="level"
                control={control}
                isRequired={false}
                value={filters.experienceLevel}
                onChange={(selected) =>
                  handleFilterChange("experienceLevel", selected?.value)
                }
              />
            </div>

            {/* Search by name */}
            <div className="flex items-center gap-2 border-[1px] focus:ring-2 focus:ring-primary p-[10px] hover:border-black border-gray-300 rounded-md">
              <div>
                <img src={searchImg} alt="search" />
              </div>
              <input
                type="text"
                placeholder="Search by name"
                className="focus:outline-none w-full"
                value={searchInput}
                onChange={handleInputChange}
              />
            </div>
            {/* clear filter */}
            <div className="flex items-center gap-2 border-[1px] focus:ring-2 focus:ring-primary p-[10px] hover:border-black border-gray-300 rounded-md">
              <button
                onClick={clearAllFilters}
                className="w-full text-center text-primary font-semibold"
              >
                Clear all Filters
              </button>
            </div>
          </div>
        </div>
        <br />

        {/* Results count and sort (desktop) */}
        <div className="hidden sm:flex items-center justify-between mb-5">
          <h3 className="text-xl text-secondary font-bold">
            {filteredTutors?.length} tutor(s) found
          </h3>
          <div className="flex items-center gap-[3px]">
            <span className="text-[#A4A4A4] text-base">Sort by</span>
            <ChevronDownIcon className="w-5 h-5 text-[#A4A4A4]" />
          </div>
        </div>
        {/* Tutor cards */}
        <div className="space-y-2">
          {filteredTutors?.length > 0 ? (
            filteredTutors?.map((tutor) => (
              <div key={tutor?.id}>
                <TutorCard tutor={tutor} />
              </div>
            ))
          ) : (
            <p className="text-gray-500 text-center">No tutors found.</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default FindTutors;
