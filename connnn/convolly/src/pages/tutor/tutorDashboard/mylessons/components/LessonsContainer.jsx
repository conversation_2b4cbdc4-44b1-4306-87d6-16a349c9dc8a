import React, { useState } from "react";
import Upcomming<PERSON>esson from "./UpcomingLesson";
import PastLessons from "./PastLessons";
import { useGetTutorClassesQuery } from "@/redux/slices/student/classesApiSlice";
import useGet from "@/hooks/useGet";

const LessonsContainer = () => {
	const [activeOption, setActiveOption] = useState("UpcomingLessons"); // Fixed typo in state

	const { data: bookings, isLoading } = useGet(useGetTutorClassesQuery, "");

	// Filter bookings into upcoming and past lessons based on the date
	const now = new Date();

	const upcomingLessons =
		bookings?.bookings?.filter(
			(lesson) => new Date(lesson.scheduledTime) > now
		) || [];

	const pastLessons =
		bookings?.bookings?.filter(
			(lesson) => new Date(lesson.scheduledTime) <= now
		) || [];

	return (
		<div className="w-full">
			{/* Toggler */}
			<div className="w-sm sm:w-[427px]">
				<div className="flex text-[#1A1A40] h-[55px] bg-gray-100 rounded-md p-1">
					<button
						className={`h-[40px] w-full p-2 m-1 mt-1 flex justify-center left-[8px] rounded-md text-center cursor-pointer transition-all duration-100 ${
							activeOption === "UpcomingLessons"
								? "bg-white shadow-sm"
								: "bg-transparent"
						}`}
						onClick={() => setActiveOption("UpcomingLessons")}
					>
						<div className="flex text-lg font-bold text-[#1A1A40]">
							<p>Upcoming Lessons</p> {/* Fixed spelling */}
						</div>
					</button>

					<button
						className={`h-[40px] w-full p-2 m-1 mt-1 flex justify-center left-[8px] rounded-md text-center cursor-pointer transition-all duration-100 ${
							activeOption === "PastLessons"
								? "bg-white shadow-sm"
								: "bg-transparent"
						}`}
						onClick={() => setActiveOption("PastLessons")}
					>
						<div className="flex text-lg font-bold text-[#1A1A40]">
							<p>Past Lessons</p>
						</div>
					</button>
				</div>
			</div>

			{/* Conditional rendering based on active option */}
			<div className="pt-6">
				{activeOption === "UpcomingLessons" ? (
					<div className="space-y-4">
						{upcomingLessons.length > 0 ? (
							upcomingLessons.map((lesson) => (
								<UpcommingLesson key={lesson.id} lesson={lesson} />
							))
						) : (
							<p>No upcoming lessons scheduled</p>
						)}
					</div>
				) : (
					<div className="space-y-4">
						{pastLessons.length > 0 ? (
							pastLessons.map((lesson) => (
								<PastLessons key={lesson?.id} lesson={lesson} />
							))
						) : (
							<p>No past lessons found</p>
						)}
					</div>
				)}
			</div>
		</div>
	);
};

export default LessonsContainer;
