import React, { useCallback, useEffect, useRef, useState } from "react";
import { useParams, useSearchParams } from "react-router-dom";
import ChatRoom from "./components/ChatRoom";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import dayjs from "dayjs";
import userVector from "@/assets/svgs/userVector.svg";
import { useGetUserConversations } from "@/api/chat";
import { useGetUserProfile } from "@/api/user";
import useInfiniteScroll from "@/hooks/useInfiniteScroll";
import { decryptMessage } from "./utils";
import Loader from "@/components/loader/loader";
import { safelyBind } from "@/utils/event";
import { CHAT_CONVERSATION } from "@/constants";
import useChatProvider from "@/hooks/useChatProvider";
import { Badge, BadgeCount } from "@/components/badge/badge";
import { cn } from "@/utils";
import PadlockIcon from "@/assets/svgs/Padlock";

export const CONVERSATION_PARAM_KEY = "chat_cid";

const Chats = () => {
  const { userId } = useParams();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();

  const [chats, setChats] = useState([]);
  const [conversationSelected, setConversationSelected] = useState(null);

  const currentUser = useSelector((state) => state?.app?.userInfo?.user);

  const conversationId = searchParams.get(CONVERSATION_PARAM_KEY) || "";

  const receiverRole = searchParams.get("role") || undefined;

  const stateRef = useRef({
    ignoreChats: false,
    selected: !!userId,
  });

  const { socketIsConnected, socket } = useChatProvider();

  const { data: otherUser, isFetching: isFetchingUser } = useGetUserProfile(
    userId,
    receiverRole
  );

  const { data: conversations, isFetching: isFetchingConversations } =
    useInfiniteScroll(useGetUserConversations(currentUser?.id));

  const normalizeToChat = useCallback(
    async (chat) => ({
      ...chat,
      unRead: chat.unRead || {},
      otherUser:
        chat.otherUser ||
        Object.values(chat.participants).filter(
          (u) => u.id !== currentUser.id
        )[0],
      ...(await decryptMessage(currentUser, chat.lastMessage)),
      lastMessageDate: chat.lastMessage?.createdAt || chat.updatedAt,
    }),
    [currentUser]
  );

  useEffect(() => {
    (async () => {
      if (stateRef.current.ignoreChats) return;

      let chats = [];

      let conversationSelected = null;

      if (conversations)
        for (const chat of conversations) {
          chats.push(await normalizeToChat(chat));
        }

      if (conversationId)
        conversationSelected =
          chats.find((c) => c.id === conversationId) || null;

      if (!conversationSelected && otherUser) {
        if (!chats.find((chat) => !!chat.participants[otherUser.id])) {
          chats = [
            {
              participants: { [otherUser.id]: otherUser },
              otherUser,
              text: "Start a new conversation",
              lastMessageDate: new Date().toISOString(),
              unRead: {},
            },
            ...chats,
          ];

          conversationSelected = chats[0];
        }
      }

      setChats(chats);
      setConversationSelected(conversationSelected);
    })();
  }, [
    currentUser,
    otherUser,
    conversations,
    normalizeToChat,
    setSearchParams,
    conversationId,
  ]);

  const onConversationChange = useCallback(
    async (conversation) => {
      console.log(conversation);
      stateRef.current.ignoreChats = true;

      let newChats = [];

      let isBool = false;

      const newChat = await normalizeToChat(conversation);

      for (const chat of chats) {
        isBool =
          isBool ||
          chat.id === conversation.id ||
          chat.otherUser.id === newChat.otherUser.id;

        newChats.push(isBool ? newChat : chat);
      }

      if (!isBool) newChats = [newChat, ...newChats];

      if (stateRef.current.selected)
        setSearchParams(
          (params) => {
            params.set(CONVERSATION_PARAM_KEY, conversation.id);
            return params;
          },
          { replace: true }
        );

      console.log(conversation);

      setChats(newChats);

      if (!conversation.lastMessage.deliveredAt && socket && otherUser?.id)
        socket.emit(
          "chat-message-delivered",
          [conversation.lastMessage.id],
          otherUser.id
        );
    },
    [chats, normalizeToChat, setSearchParams, socket, otherUser?.id]
  );

  useEffect(() => {
    if (socket) safelyBind(socket, CHAT_CONVERSATION, onConversationChange);
  }, [socket, onConversationChange]);

  const handleChatClick = (chat) => {
    setConversationSelected(chat);

    navigate(
      `/${currentUser?.role}/messages/${
        chat.otherUser.id
      }?${CONVERSATION_PARAM_KEY}=${chat.id || ""}`
    );
  };

  const isFetching =
    isFetchingUser || isFetchingConversations || !socketIsConnected;

  if (isFetching) return <Loader fullscreen />;

  const withConversation = conversationId || !!conversationSelected;

  return (
    <div className="w-full h-full">
      <div className="w-full sm:flex h-full gap-5">
        <div
          className={cn(
            `
        w-full border rounded-md bg-gray-50 
        overflow-hidden sm:max-w-[40%]
          `,
            !chats.length && "hidden"
          )}
        >
          <div className="overflow-y-auto h-full">
            {isFetching ? (
              <p className="text-gray-500 text-center p-4">
                Loading conversations...
              </p>
            ) : chats.length > 0 ? (
              chats.map((chat, i) => {
                return (
                  <div
                    key={i}
                    onClick={() => handleChatClick(chat)}
                    className={`p-3 cursor-pointer hover:bg-gray-100 flex items-center gap-3 border-b ${
                      chat.otherUser.id === otherUser?.id
                        ? "bg-[#EBEDF0]"
                        : "bg-white"
                    }`}
                  >
                    <img
                      src={chat.otherUser.image || userVector}
                      alt={chat.otherUser.firstname}
                      className="w-10 h-10 rounded-full object-cover"
                    />

                    <div className="w-full">
                      <div className="flex justify-between items-center">
                        <div>
                          <p className="font-medium truncate">
                            {chat.otherUser
                              ? chat.otherUser.fullname
                              : "Unknown User"}
                          </p>

                          <p className="">{chat.text}</p>
                        </div>

                        <div>
                          <div className="text-xs text-gray-500 whitespace-nowrap">
                            {dayjs(chat.lastMessageDate).format("MMM D")}
                          </div>

                          <BadgeCount count={chat.unRead[currentUser.id]} />
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })
            ) : (
              <p className="text-gray-500 text-center p-4">
                Start a conversation with someone
              </p>
            )}
          </div>
        </div>

        <div
          className={cn(
            "w-full sm:max-w-[60%]",
            !chats.length && "sm:max-w-full"
          )}
        >
          {withConversation ? (
            <ChatRoom
              key={conversationId}
              withUnRead={!!conversationSelected.unRead[currentUser.id]}
              conversationId={conversationId}
              otherUser={otherUser}
            />
          ) : (
            <div
              className="
            flex flex-col items-center justify-center 
            h-full gap-2 w-full
            "
            >
              <div
                className="
              flex-1 flex flex-col items-center justify-center gap-2
              w-full
              "
              >
                {chats?.length ? (
                  <>
                    <p className="font-bold text-[22px] text-[#1A1A40] font-fig-tree">
                      Start chatting
                    </p>

                    <p
                      className="
              font-medium font-fig-tree text-[18px] 
              text-[#AAAAAA]
              "
                    >
                      Select a chat and continue where you left off.
                    </p>
                  </>
                ) : (
                  <>
                    <p className="font-bold text-[22px] text-[#1A1A40] font-fig-tree">
                      Your inbox is empty for now
                    </p>

                    <p
                      className="
              font-medium font-fig-tree text-[18px] 
              text-[#AAAAAA]
              "
                    >
                      New message will appear here once someone reaches out.
                      Stay tuned!
                    </p>
                  </>
                )}
              </div>

              <div className="flex items-center gap-2">
                <PadlockIcon />
                <p
                  className="
                text-[#AAAAAA] font-fig-tree font-medium text-[18px]
                "
                >
                  End-to-end encrypted
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Chats;
