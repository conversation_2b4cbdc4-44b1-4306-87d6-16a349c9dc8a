import React from "react";
import { Badge } from "../../../../components/badge/badge";
import { <PERSON>ton } from "../../../../components/button/button";
import { Card, CardContent } from "../../../../components/card/card";
import fullStar from "../../../../assets/svgs/fullStar.svg";
import halfStar from "../../../../assets/svgs/halfStar.svg";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay } from "swiper/modules";
import "swiper/css";
import { useGetTutorsQuery } from "@/redux/slices/student/findTutorApiSlice";
import userVector from "@/assets/svgs/userVector.svg";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";

export const FeaturedTutors = () => {
	const user = useSelector((state) => state?.app?.userInfo?.user);
	const navigate = useNavigate();

	const {
		data: tutorsResponse,
		isLoading,
		refetch,
		error,
	} = useGetTutorsQuery();

	const tutors = tutorsResponse?.data || [];

	console.log(
		"tutors",
		tutors.map((tutor) => tutor._id)
	);

	const bookFreeTrial = (e) => {
		e.stopPropagation();

		if (!user) {
			navigate("/signin");
			return;
		}

		if (user.role === "student") {
			navigate(`/student/my-lessons?tab=tutors`);
		} else if (user.role === "tutor") {
			navigate("/tutor/dashboard");
		}
	};

	const renderStars = (rating) => {
		const fullStars = Math.floor(rating);
		const hasHalfStar = rating % 1 !== 0;

		return (
			<div className="inline-flex items-center">
				{[...Array(fullStars)].map((_, i) => (
					<img key={i} className="w-[16.67px] h-4" alt="Star" src={fullStar} />
				))}
				{hasHalfStar && (
					<img
						className="w-[15.23px] h-[14.48px]"
						alt="Half star"
						src={halfStar}
					/>
				)}
			</div>
		);
	};

	return (
		<section className="w-full py-[50px] sm:px-8 px-3 bg-gray-50">
			<div className="flex flex-col gap-12 max-w-[1440px] mx-auto">
				<h2 className="font-semibold text-secondary text-[30px] tracking-tight leading-[44px] sm:text-[46px]">
					Featured Tutors
				</h2>

				<div className="relative overflow-hidden">
					<Swiper
						modules={[Autoplay]}
						slidesPerView={4}
						spaceBetween={30}
						loop={true}
						speed={9500}
						allowTouchMove={true}
						centeredSlides={false}
						autoplay={{
							delay: 0,
							disableOnInteraction: false,
							reverseDirection: false,
						}}
						className="w-full !overflow-visible"
						style={{
							paddingLeft: "0px",
							paddingRight: "0px",
						}}
						// Add responsive breakpoints for mobile view
						breakpoints={{
							// when window width is >= 320px (mobile)
							320: {
								slidesPerView: 1,
								spaceBetween: 20,
							},
							// when window width is >= 640px (tablet)
							640: {
								slidesPerView: 2,
								spaceBetween: 20,
							},
							// when window width is >= 768px (desktop)
							768: {
								slidesPerView: 3,
								spaceBetween: 25,
							},
							// when window width is >= 1024px (large desktop)
							1024: {
								slidesPerView: 4,
								spaceBetween: 30,
							},
						}}
					>
						{tutors.map((tutor, index) => (
							<SwiperSlide key={index} className="!h-auto">
								<Card className="bg-white rounded-lg group overflow-hidden h-full flex flex-col">
									<CardContent className="p-0 flex-1 flex flex-col">
										<div className="flex flex-col gap-3 h-full">
											{/* Fixed image container with consistent aspect ratio */}
											<div className="relative w-full aspect-[4/3] overflow-hidden rounded-t-lg">
												<img
													src={tutor?.image || userVector}
													alt={tutor?.name}
													className="absolute inset-0 w-full h-full object-center group-hover:scale-105 transition-all duration-300"
													onError={(e) => {
														// Fallback image if the main image fails to load
														e.target.src = userVector;
													}}
												/>
											</div>

											{/* Content section with consistent padding and flex growth */}
											<div className="flex flex-col gap-6 p-4 flex-1 justify-between">
												<div className="flex flex-col gap-3">
													<div className="flex flex-col gap-2">
														<div className="flex items-start justify-between gap-2">
															<h3 className="flex-1 font-semibold text-secondary text-[18px] leading-[24px] tracking-normal line-clamp-1">
																{tutor?.fullname}
															</h3>

															<Badge className="bg-white text-primary rounded-full px-2 py-1 text-[14px] font-medium leading-[20px] tracking-normal whitespace-nowrap flex-shrink-0">
																{tutor?.category}
															</Badge>
														</div>

														<p className="text-gray-600 text-[16px] leading-[24px] font-normal tracking-normal line-clamp-2">
															{Array.isArray(tutor.languages)
																? tutor.languages
																		.map((lang) => lang.name)
																		.join(", ")
																: tutor.languages}
														</p>
													</div>

													<div className="flex items-center gap-3">
														{renderStars(tutor?.rating)}
														<span className="flex-1 text-gray-600 text-[16px] leading-[24px] font-normal tracking-normal">
															{tutor?.rating?.toFixed(1)} ({tutor?.reviews}{" "}
															reviews)
														</span>
													</div>
												</div>

												{/* Fixed bottom section */}
												<div className="flex items-center justify-between pt-2">
													<p className="font-semibold text-secondary text-[18px] leading-[24px] tracking-normal">
														${tutor?.basePrice}/hour
													</p>

													<Button
														onClick={(e) => bookFreeTrial(e, tutor)}
														className="bg-primary text-white rounded-lg px-4 py-2 text-[16px] font-normal leading-[24px] tracking-normal flex-shrink-0"
													>
														Book Trial
													</Button>
												</div>
											</div>
										</div>
									</CardContent>
								</Card>
							</SwiperSlide>
						))}
					</Swiper>
				</div>
			</div>
		</section>
	);
};
