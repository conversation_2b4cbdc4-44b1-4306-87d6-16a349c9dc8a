import React, { useState, useEffect } from "react";
import { CustomSelect } from "@/components/select/select";
import { useForm } from "react-hook-form";
import { useSelector, useDispatch } from "react-redux";
import { handleProfileUpdateResponse } from "@/utils/profileUtils";
import MultiSelect from "@/components/select/multiSelect";
import Morning from "@/assets/svgs/morning";
import Afternoon from "@/assets/svgs/afternoon";
import Evening from "@/assets/svgs/evening";
import { Button } from "@/components/button/button";
import usePost from "@/hooks/usePost";
import { useUpdateProfileMutation } from "@/redux/slices/onboardingApiSlice";
import SuccessModal from "@/components/modal/successModal";
import { useNavigate } from "react-router-dom";
import Loader from "@/components/loader/loader";

const PageTwo = ({ setActiveTab, studentDetails, refetchStudentDetails }) => {
  const studentId = useSelector((state) => state?.app?.userInfo?.user?.id);
  const currentUser = useSelector((state) => state?.app?.userInfo?.user);
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const [selectedDurations, setSelectedDurations] = useState([]);
  const [showSuccess, setShowSuccess] = useState(false);

  const {
    control,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm();

  const levelOptions = [
    { label: "Beginner", value: "Beginner" },
    { label: "Intermediate", value: "Intermediate" },
    { label: "Advanced", value: "Advanced" },
    { label: "Fluent", value: "Fluent" },
  ];

  const timezones = [
    { value: "Africa/Lagos", label: "Lagos (UTC+1)" },
    { value: "America/New_York", label: "New York (UTC-5/-4)" },
    { value: "America/Los_Angeles", label: "Los Angeles (UTC-8/-7)" },
    { value: "Europe/London", label: "London (UTC+0/+1)" },
    { value: "Asia/Kolkata", label: "Kolkata (UTC+5:30)" },
    { value: "Asia/Tokyo", label: "Tokyo (UTC+9)" },
    { value: "Australia/Sydney", label: "Sydney (UTC+10/+11)" },
    { value: "Europe/Paris", label: "Paris (UTC+1/+2)" },
    { value: "Europe/Berlin", label: "Berlin (UTC+1/+2)" },
    { value: "Africa/Cairo", label: "Cairo (UTC+2)" },
    { value: "America/Chicago", label: "Chicago (UTC-6/-5)" },
    { value: "America/Denver", label: "Denver (UTC-7/-6)" },
    { value: "America/Sao_Paulo", label: "São Paulo (UTC-3)" },
    { value: "Asia/Dubai", label: "Dubai (UTC+4)" },
    { value: "Asia/Shanghai", label: "Shanghai (UTC+8)" },
    { value: "Asia/Singapore", label: "Singapore (UTC+8)" },
    { value: "Europe/Moscow", label: "Moscow (UTC+3)" },
    { value: "Africa/Johannesburg", label: "Johannesburg (UTC+2)" },
    { value: "Pacific/Auckland", label: "Auckland (UTC+12/+13)" },
    { value: "America/Toronto", label: "Toronto (UTC-5/-4)" },
  ];

  const daysOfWeek = [
    { label: "Monday", value: "Monday" },
    { label: "Tuesday", value: "Tuesday" },
    { label: "Wednesday", value: "Wednesday" },
    { label: "Thursday", value: "Thursday" },
    { label: "Friday", value: "Friday" },
    { label: "Saturday", value: "Saturday" },
    { label: "Sunday", value: "Sunday" },
  ];

  const timeOfDay = [
    { name: "Morning", time: "9-11", Icon: Morning },
    { name: "Afternoon", time: "12-18", Icon: Afternoon },
    { name: "Evening", time: "19-22", Icon: Evening },
  ];

  const { handlePost: handleUpdateStudent, isLoading: updating } = usePost(
    useUpdateProfileMutation
  );

  const onboardingComplete = () => {
    setShowSuccess(false);
    navigate("/student/dashboard");
  };

  const updatePageTwo = async (data) => {
    const timeAvailable = selectedDurations.map((label) => {
      const timeSlot = timeOfDay.find(
        (t) => t.name.toLowerCase() === label.toLowerCase()
      );
      const [from, to] = timeSlot.time.split("-");
      return { label, from, to };
    });

    const { level, ...rest } = data;

    const res = await handleUpdateStudent({
      ...rest,
      timeAvailable,
      languages: [{ name: "English", level }],
      location: { address: data?.location },
      userId: studentId,
      role: "student",
    });

    // Update Redux store with new user data
    handleProfileUpdateResponse(dispatch, currentUser, res);

    if (res) {
      setShowSuccess(true);
      refetchStudentDetails();
    }
  };

  useEffect(() => {
    if (studentDetails) {
      setValue("level", studentDetails?.languages?.[0]?.level || "");
      setValue("daysAvailable", studentDetails?.daysAvailable || []);
      setValue("timezone", studentDetails?.timezone || "");
      setValue(
        "preferredLessonDuration",
        studentDetails?.preferredLessonDuration || ""
      );

      const timeAvailableLabels =
        studentDetails?.timeAvailable?.map((slot) =>
          slot.label.toLowerCase()
        ) || [];
      setSelectedDurations(timeAvailableLabels);
    }
  }, [studentDetails, setValue]);

  return (
    <>
      {updating && <Loader />}

      <SuccessModal
        isOpen={showSuccess}
        onClose={onboardingComplete}
        title="Congratulations"
        message="You have onboarded successfully"
        onButtonClick={onboardingComplete}
        buttonText="Continue"
      />

      <form onSubmit={handleSubmit(updatePageTwo)}>
        <CustomSelect
          placeholder="Select your English level"
          label="What is your current English level?"
          options={levelOptions}
          control={control}
          name="level"
          isRequired={true}
          error={errors?.level?.message}
          className="p-5 py-[22px]"
          parentClassName="mb-7"
        />

        <MultiSelect
          options={daysOfWeek}
          placeholder="Select days of the week you will take lessons"
          label="When can you take lessons?"
          control={control}
          name="daysAvailable"
          parentClassName="mb-7"
        />

        <p className="max-sm:text-sm text-secondary mb-2">
          What is your preferred lesson duration?
        </p>

        {timeOfDay.map((item) => (
          <label className={`flex items-center gap-2 w-fit mb-2`} key={name}>
            <input
              type="checkbox"
              value={item.name.toLowerCase()}
              checked={selectedDurations.includes(item.name.toLowerCase())}
              onChange={(e) => {
                const { checked, value } = e.target;
                setSelectedDurations((prev) =>
                  checked
                    ? [...prev, value]
                    : prev.filter((val) => val !== value)
                );
              }}
              className="custom-checkbox text-sm border border-[#E8E8E8] bg-white rounded-xl focus:outline-none"
            />
            <div className="sm:text-base text-sm flex gap-2">
              <item.Icon />
              {item.name + " (" + item.time + ")"}
            </div>
          </label>
        ))}

        <CustomSelect
          placeholder="Select your timezone"
          label="Choose your time zone"
          options={timezones}
          control={control}
          name="timezone"
          isRequired={true}
          error={errors?.timezone?.message}
          className="p-5 py-[22px]"
          parentClassName="mb-7"
        />

        <div className="sm:flex gap-5 mt-10">
          <Button
            className="w-full h-[50px] bg-white border-[#D2D2D2] text-secondary border hover:bg-white"
            onClick={() => setActiveTab("pageOne")}
          >
            Back
          </Button>

          <Button className="w-full h-[50px]" disabled={updating} type="submit">
            Save And Continue
          </Button>
        </div>
      </form>
    </>
  );
};

export default PageTwo;
