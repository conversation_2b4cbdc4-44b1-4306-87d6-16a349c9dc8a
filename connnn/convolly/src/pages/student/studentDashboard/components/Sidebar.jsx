import React, { useEffect, useRef } from "react";
import Links from "./Links";
import { useNavigate } from "react-router-dom";
import img from "@/assets/svgs/userVector.svg";
import { useSelector } from "react-redux";

const Sidebar = ({ isOpen, toggleSidebar, setShowLogoutModal }) => {
	const sidebarRef = useRef(null);
	const navigate = useNavigate();
	const user = useSelector((state) => state?.app?.userInfo?.user);

	useEffect(() => {
		const handleClickOutside = (event) => {
			const navbarToggle = document.querySelector(
				".lg\\:hidden.p-1.rounded-md"
			);

			if (
				sidebarRef.current &&
				!sidebarRef.current.contains(event.target) &&
				isOpen &&
				event.target !== navbarToggle &&
				!navbarToggle?.contains(event.target)
			) {
				toggleSidebar();
			}
		};

		document.addEventListener("mousedown", handleClickOutside);
		return () => document.removeEventListener("mousedown", handleClickOutside);
	}, [isOpen, toggleSidebar]);

	const handleRoute = () => {
		navigate("/student/my-lessons?tab=tutors");
		toggleSidebar();
	};

	return (
		<div
			ref={sidebarRef}
			className={`fixed lg:relative z-50 lg:z-auto lg:block lg:w-[320px] md:w-[280px]  w-[245px] shadow-lg p-3 overflow-auto h-full bg-white transition-all duration-300 ${
				isOpen ? "left-0" : "-left-full"
			} lg:left-0`}
		>
			{/* Main Content */}
			<div className="flex-1 overflow-y-auto">
				{/* User Profile Section */}
				<div className="my-4 lg:my-3">
					<div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-shadow duration-200">
						<div className="flex items-center p-4 gap-3">
							<div className="relative">
								<img
									src={user?.image || img}
									alt={user?.fullname}
									className="w-14 h-14 rounded-full object-cover border-2 border-white shadow-md"
								/>
								<div className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
							</div>

							<div className="min-w-0">
								<h3 className="font-medium text-gray-900 truncate">
									{user?.fullname || "User"}
								</h3>
								<p className="text-xs sm:text-sm text-gray-600 capitalize">
									{user?.role?.toLowerCase() || "student"}
								</p>
							</div>
						</div>
					</div>
				</div>

				{/* Navigation Links */}
				<div className="mb-2">
					<Links
						toggleSidebar={toggleSidebar}
						setShowLogoutModal={setShowLogoutModal}
					/>
				</div>
				<div className="bg-[#EBEDF0] p-2 mt-6 sm:mt-2 rounded-md mb-4">
					<div className="p-3">
						<p className="text-sm sm:text-md lg:text-[18px] text-[#1A1A40]">
							Subscribe to your favourite tutor
						</p>
						<p className="text-sm sm:text-sm lg:text-[14px] mt-2 sm:mt-4 text-[#4B5563]">
							Love the way a tutor teaches? Stay consistent and build real
							progress by subscribing to them
						</p>
					</div>
					<button
						onClick={handleRoute}
						className="w-full mt-4 text-[18px] text-white rounded-md text-center bg-primary px-2 py-4"
					>
						Subscribe
					</button>
				</div>
			</div>
		</div>
	);
};

export default Sidebar;
