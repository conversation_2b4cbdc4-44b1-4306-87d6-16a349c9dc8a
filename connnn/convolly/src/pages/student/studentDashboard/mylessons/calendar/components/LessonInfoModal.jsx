import { formatTutorName } from "@/utils/utils";
import { format } from "date-fns";
import { X } from "lucide-react";
import React from "react";
import { useNavigate } from "react-router-dom";

const LessonInfoModal = ({
	selectedLesson,
	onClose,
	onReschedule,
	onCancel,
}) => {
	if (!selectedLesson) return null;

	const navigate = useNavigate();

	return (
		<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
			<div className="w-full sm:w-[700px] bg-white rounded-lg shadow-lg p-4 relative z-50">
				<div className="flex justify-between items-center pb-4 border-blue-300">
					<h2 className="text-2xl font-bold text-gray-800">
						Lesson information
					</h2>
					<button
						className="text-gray-600 hover:text-gray-800"
						onClick={onClose}
					>
						<X size={30} />
					</button>
				</div>

				<div className="mt-4">
					<div className="border p-2 rounded-md">
						<div className="flex border-b  pb-4 items-center space-x-4">
							<img
								src={selectedLesson.image}
								alt="Teacher"
								className="object-cover w-16 h-16 rounded-md"
							/>
							<div className="p-2  space-y-2">
								<p className="text-[#1A1A40]  font-semibold">
									{selectedLesson.frequency}
								</p>
								<p className="text-[#4B5563]">
									{selectedLesson.formattedTime} -{" "}
									{format(selectedLesson.endTime, "h:mm a")}
								</p>
							</div>
						</div>

						<div className="text-[14px] font-sans text-[#4B5563] flex justify-between px-2 mt-4">
							<p>{formatTutorName(selectedLesson.tutor)}</p>
							<p>{selectedLesson.title}</p>
							<p>{selectedLesson.status}</p>
						</div>
					</div>

					<div className="mt-4">
						<button
							onClick={() => navigate(`/student/messages`)}
							className="h-50  w-full font-semibold bg-primary text-white py-2 rounded-md"
						>
							Message
						</button>
						<button
							onClick={() =>
								navigate(
									`/classroom/${selectedLesson.bookingData.classroom.id}`,
									{
										state: selectedLesson,
									}
								)
							}
							className="h-50 mt-2 w-full font-semibold bg-white text-[#333333] py-2 rounded-md border border-gray-300 hover:bg-gray-100"
						>
							Open classroom
						</button>
						<button
							className="h-50 mt-2 w-full font-semibold bg-white text-[#333333] py-2 rounded-md border border-gray-300 hover:bg-gray-100"
							onClick={onReschedule}
						>
							Reschedule lesson
						</button>
						<button
							className="h-50 mt-2 w-full font-semibold bg-[#D00416] text-white py-2 rounded-md hover:bg-red-600"
							onClick={onCancel}
						>
							Cancel lesson
						</button>
					</div>
				</div>
			</div>
		</div>
	);
};

export default LessonInfoModal;
