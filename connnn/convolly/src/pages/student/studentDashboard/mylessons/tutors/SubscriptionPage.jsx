import React, { useState } from "react";
import { useNavigate, useLocation, useParams } from "react-router-dom";
import { X, ChevronRight, Star } from "lucide-react";
import PaymentMethods from "@/pages/payments/components/PaymentMethods";
import SuccessModal from "@/components/modal/successModal";
import usePost from "@/hooks/usePost";
import useGet from "@/hooks/useGet";
import { useSubscribeToTutorMutation } from "@/redux/slices/student/subscriptionApiSlice";
import { capitalizeWords, formatTutorName } from "@/utils/utils";
import { useSelector } from "react-redux";
import { useGetTutorDetailsQuery } from "@/redux/slices/student/findTutorApiSlice";

const SubscriptionPage = () => {
	const [showCheckout, setShowCheckout] = useState(false);
	const [selectedPlan, setSelectedPlan] = useState(1);
	const [showSuccessOverlay, setShowSuccessOverlay] = useState(false);
	const navigate = useNavigate();
	const { id: tutorId } = useParams();

	// use the generic post mutation hook to handle the login mutation
	const { handlePost: handleSubscribe, isLoading: subscribing } = usePost(
		useSubscribeToTutorMutation
	);

	const { data: tutor, isLoading: gettingTutor } = useGet(
		useGetTutorDetailsQuery,
		tutorId,
		!!tutorId
	);

	const user = useSelector((state) => state?.app?.userInfo?.user);

	const handlePaymentComplete = async (cardDetails) => {
		const payload = {
			userId: user?.id,
			tutorId,
			lessonsPerWeek: selectedPlan,
			cardDetails,
			role: "student",
			email: user?.email,
		};

		const res = await handleSubscribe(payload);

		if (res) {
			setShowSuccessOverlay(true);
		}
	};

	const closeOverlay = () => {
		setShowSuccessOverlay(false);
	};

	const handleContinue = () => {
		setShowSuccessOverlay(false);
		navigate("/student/dashboard");
	};

	return (
		<>
			{showCheckout ? (
				<>
					<div className="flex sm:flex-row items-start gap-5">
						<div className="w-full border rounded-lg p-3 sm:p-5">
							<h2 className="text-xl font-semibold mb-1">
								Good choice. Last step!
							</h2>
							<p className="text-gray-600 mb-5">
								Enter your details to confirm your monthly subscription.
							</p>

							<h2 className="text-xl font-semibold mb-2">Your order</h2>

							<div className="border-t border-b py-3 mb-3">
								<div className="flex justify-between items-center mb-1">
									<p className="text-gray-600">
										{selectedPlan * 4} lessons ($ {tutor?.basePrice} / lesson)
									</p>

									<p className="text-gray-600">
										$ {selectedPlan * 4 * tutor?.basePrice}.00
									</p>
								</div>

								<div className="flex justify-between items-center mb-1">
									<p className="text-gray-600">Processing fee</p>

									{/* 8% */}
									<p className="text-gray-600">
										${0.32 * selectedPlan * tutor.basePrice}
									</p>
								</div>

								<div className="flex justify-between items-center">
									<h2 className="text-xl font-bold mb-2">Total</h2>

									<p className="text-gray-600">
										${" "}
										{selectedPlan * 4 * tutor?.basePrice +
											0.32 * selectedPlan * tutor.basePrice}
									</p>
								</div>
							</div>

							<PaymentMethods
								onCompletePayment={handlePaymentComplete}
								lessonsPerWeek={selectedPlan}
								tutor={tutor}
							/>
						</div>

						<div className="border rounded-lg p-3 sm:p-5 w-full">
							<h2 className="text-xl font-semibold mb-4">
								{selectedPlan} lesson{selectedPlan > 1 ? "s" : ""} per week
							</h2>
							<p className="text-gray-600 mb-6">
								That's {selectedPlan * 4 * tutor?.basePrice} lessons every 4
								weeks at ${selectedPlan * 12 * tutor?.basePrice}
								.00.
							</p>

							<ul className="space-y-3 mb-8">
								<li className="flex items-start">
									<span className="text-primary mr-2">•</span>
									<span>
										Schedule your {selectedPlan * 4} lessons for any time during
										the 4 week period
									</span>
								</li>
								<li className="flex items-start">
									<span className="text-primary mr-2">•</span>
									<span>Change your tutor for free at any time</span>
								</li>
								<li className="flex items-start">
									<span className="text-primary mr-2">•</span>
									<span>Cancel your plan at any time</span>
								</li>
								<li className="flex items-start">
									<span className="text-primary mr-2">•</span>
									<span>Change the duration of your classes at any time</span>
								</li>
							</ul>

							<div className="flex items-center border-t pt-3">
								<div className="w-12 h-12 rounded-full overflow-hidden mr-4">
									<img
										src={tutor?.image}
										alt="Tutor"
										className="w-full h-full object-cover"
									/>
								</div>
								<div>
									<p className="font-medium">
										{formatTutorName(tutor?.fullname)}{" "}
									</p>
									<div className="flex items-center">
										<Star className="w-4 h-4 text-yellow-500 fill-yellow-500 mr-1" />
										<span>4.5 (20 Reviews)</span>
									</div>
								</div>
							</div>
						</div>

						{showSuccessOverlay && (
							<div className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-30">
								<SuccessModal
									isOpen={showSuccessOverlay}
									onClose={closeOverlay}
									onContinue={handleContinue}
								/>
							</div>
						)}
					</div>
				</>
			) : (
				<div className="sm:flex gap-5 sm:max-w-[90%] mx-auto items-start">
					<div className="border rounded-lg p-3 sm:p-5 w-full">
						<h1 className="text-2xl font-bold mb-6">Get started</h1>

						<div className="mb-8">
							<p className="text-gray-600 mb-6">
								Consistency is key to progress, so we recommend a weekly
								schedule. Each lesson costs ${tutor?.basePrice}
							</p>

							<div className="grid gap-4 mb-8">
								{[1, 2, 3, 4, 5].map((lessons) => (
									<div
										key={lessons}
										className={`border rounded-lg p-4 cursor-pointer transition-colors ${
											selectedPlan === lessons
												? "border-primary bg-primary/10"
												: "border-gray-200 hover:bg-gray-50"
										}`}
										onClick={() => setSelectedPlan(lessons)}
									>
										<div className="flex justify-between items-center">
											<p className="font-medium">
												<span className="font-bold text-lg mr-1">
													{lessons} {` `}
													lesson
													{lessons > 1 ? "s" : ""}
												</span>
												per week
											</p>

											<p className="text-gray-600">
												${lessons * 4}.00 every 4 weeks
											</p>
										</div>
									</div>
								))}
							</div>

							<button
								className="w-full bg-primary text-white py-3 rounded-lg font-medium hover:bg-primary-dark transition"
								onClick={() => setShowCheckout(true)}
							>
								Continue to checkout
							</button>
						</div>
					</div>

					<div className="border rounded-lg p-3 sm:p-5 w-full">
						<h2 className="text-xl font-semibold mb-4">
							{selectedPlan} lesson{selectedPlan > 1 ? "s" : ""} per week
						</h2>
						<p className="text-gray-600 mb-6">
							That's {selectedPlan * 4 * tutor?.basePrice} lessons every 4 weeks
							at ${selectedPlan * 4 * tutor?.basePrice}
							.00.
						</p>

						<ul className="space-y-3 mb-8">
							<li className="flex items-start">
								<span className="text-primary mr-2">•</span>
								<span>
									Schedule your {selectedPlan * 4} lessons for any time during
									the 4 week period
								</span>
							</li>
							<li className="flex items-start">
								<span className="text-primary mr-2">•</span>
								<span>Change your tutor for free at any time</span>
							</li>
							<li className="flex items-start">
								<span className="text-primary mr-2">•</span>
								<span>Cancel your plan at any time</span>
							</li>
							<li className="flex items-start">
								<span className="text-primary mr-2">•</span>
								<span>Change the duration of your classes at any time</span>
							</li>
						</ul>

						<div className="flex items-center border-t pt-3">
							<div className="w-12 h-12 rounded-full overflow-hidden mr-4">
								<img
									src={tutor?.image}
									alt="Tutor"
									className="w-full h-full object-cover"
								/>
							</div>
							<div>
								<p className="font-medium">
									{capitalizeWords(tutor?.fullname)}{" "}
								</p>
								<div className="flex items-center">
									<Star className="w-4 h-4 text-yellow-500 fill-yellow-500 mr-1" />
									<span>4.5 (20 Reviews)</span>
								</div>
							</div>
						</div>
					</div>
				</div>
			)}
		</>
	);
};

export default SubscriptionPage;
