import React from "react";
import star from "@/assets/svgs/fullStar.svg";
import RateTutorModal from "../../reviews/components/RateTutorModal";
import { format } from "date-fns";
import userVector from "@/assets/svgs/userVector.svg";
import { capitalizeWords, formatTutorName } from "@/utils/utils";
import { useCreateReviewMutation } from "@/redux/slices/student/reviewApiSlice";
import { useSelector } from "react-redux";
import { toast } from "react-toastify";

const PastLessons = ({ lesson }) => {
	const [showRatingModal, setShowRatingModal] = React.useState(false);
	const [reviewTutor] = useCreateReviewMutation();
	const user = useSelector((state) => state?.app?.userInfo?.user);

	const date = format(new Date(lesson.scheduledTime), "EEEE, MMMM d, HH:mm");

	const openModal = () => {
		setShowRatingModal(true);
	};

	const closeModal = () => {
		setShowRatingModal(false);
	};

	const handleSubmitReview = async ({ rating, comment }) => {
		try {
			if (!user) {
				throw new Error("User not authenticated");
			}

			await reviewTutor({
				id: user.id, // For the URL
				targetId: lesson?.tutor?.id,
				targetRole: "tutor",
				rating,
				comment,
				lessonId: lesson?.id,
			}).unwrap();

			// Success handling
			setShowRatingModal(false);
		} catch (error) {
			console.error("Review submission failed:", error);
			toast.error(
				`Failed to submit review: ${error.data?.message || error.message}`
			);
		}
	};

	return (
		<div className="border rounded-lg p-3 sm:p-4 md:p-6">
			<div className="flex flex-col sm:flex-row sm:justify-between gap-3 sm:gap-4">
				{/* Main content section */}
				<div className="flex flex-1 min-w-0">
					<img
						src={lesson?.tutorImage || userVector}
						alt="Tutor profile"
						className="object-cover w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 rounded-lg flex-shrink-0 mr-3 sm:mr-4"
					/>
					<div className="min-w-0 flex-1">
						{/* Date and time */}
						<h3 className="text-[#1A1A40] text-sm sm:text-base md:text-lg lg:text-xl font-semibold leading-tight mb-1 sm:mb-2">
							{date}
						</h3>

						{/* Payment info and tutor details */}
						<div className="text-[#4B5563] text-xs sm:text-sm space-y-1 sm:space-y-0">
							<div className="flex flex-wrap items-start gap-1">
								<span className="font-medium">
									{formatTutorName(lesson?.tutor?.name)},
								</span>
								<span className="break-words">{lesson?.title}</span>
							</div>
						</div>
					</div>
				</div>

				{/* Rate button section */}
				<div className="flex sm:flex-shrink-0 sm:items-start">
					<button
						onClick={openModal}
						className="border border-primary hover:bg-primary hover:text-white transition-colors py-2.5 px-4 sm:px-6 rounded-lg text-primary text-sm sm:text-base font-medium w-full sm:w-auto sm:min-w-[120px] flex items-center justify-center gap-2 focus:outline-none focus:ring-2 focus:ring-primary/50"
					>
						<img
							src={star}
							alt="Rate icon"
							className="w-4 h-4 sm:w-5 sm:h-5 flex-shrink-0"
						/>
						<span>Rate</span>
					</button>
				</div>
			</div>

			{/* Modal */}
			{showRatingModal && (
				<div className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50 p-4">
					<div className="w-full max-w-md sm:max-w-2xl max-h-full overflow-auto">
						<RateTutorModal
							onClose={closeModal}
							tutorName={capitalizeWords(lesson?.tutor?.name)}
							onSubmitReview={handleSubmitReview}
						/>
					</div>
				</div>
			)}
		</div>
	);
};

export default PastLessons;
