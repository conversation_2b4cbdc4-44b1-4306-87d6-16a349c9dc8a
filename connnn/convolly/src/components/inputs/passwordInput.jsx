import React, { useState } from "react";
import eyeOpen from "../../assets/svgs/eyeOpen.svg";
import eyeClosed from "../../assets/svgs/eyeClosed.svg";

const PasswordInput = ({
  register,
  fieldName,
  placeHolder,
  defaultValue,
  autoComplete,
  disabled,
  sizeClass,
  registerOptions,
  validate,
}) => {
  const [showPassword, setShowPassword] = useState(false);
  const [password, setPassword] = useState("");

  const checks = {
    hasUpperCase: /[A-Z]/.test(password),
    hasLowerCase: /[a-z]/.test(password),
    hasSpecialChar: /[^A-Za-z0-9]/.test(password),
    hasMinLength: password.length >= 8,
  };

  const inputProps = register(fieldName, registerOptions);

  return (
    <div className="relative w-full">
      <input
        className={`text-sm sm:text-base border ${sizeClass} border-[#E8E8E8] bg-white p-3 px-4 rounded-lg w-full satoshi focus:outline-none ${
          disabled ? "cursor-not-allowed" : ""
        }`}
        type={showPassword ? "text" : "password"}
        placeholder={placeHolder}
        defaultValue={defaultValue}
        {...inputProps}
        autoComplete={autoComplete}
        disabled={disabled}
        onChange={(e) => {
          setPassword(e.target.value);
          inputProps.onChange(e);
        }}
      />
      <span
        className="absolute right-4 top-[26px] transform -translate-y-1/2 cursor-pointer"
        onClick={() => setShowPassword(!showPassword)}
      >
        <img src={showPassword ? eyeOpen : eyeClosed} alt="eye icon" />
      </span>

      {/* Password Strength Checks */}
      {validate && (
        <div className="mt-2 space-y-1 text-sm">
          <p
            className={checks.hasMinLength ? "text-green-600" : "text-gray-500"}
          >
            {checks.hasMinLength ? "✓" : "✗"} At least 8 characters
          </p>
          <p
            className={checks.hasUpperCase ? "text-green-600" : "text-gray-500"}
          >
            {checks.hasUpperCase ? "✓" : "✗"} At least one uppercase letter
          </p>
          <p
            className={checks.hasLowerCase ? "text-green-600" : "text-gray-500"}
          >
            {checks.hasLowerCase ? "✓" : "✗"} At least one lowercase letter
          </p>
          <p
            className={
              checks.hasSpecialChar ? "text-green-600" : "text-gray-500"
            }
          >
            {checks.hasSpecialChar ? "✓" : "✗"} At least one special character
          </p>
        </div>
      )}
    </div>
  );
};

export default PasswordInput;
