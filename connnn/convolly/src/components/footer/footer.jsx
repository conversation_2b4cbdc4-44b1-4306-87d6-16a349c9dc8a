import { Facebook, Twitter, Linkedin, Instagram } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import Logo from "../../assets/svgs/logo2.svg";
import twitter from "../../assets/svgs/twitter.svg";
import facebook from "../../assets/svgs/facebook.svg";
import linkedIn from "../../assets/svgs/linkedIn.svg";
import instagram from "../../assets/svgs/instagram.svg";
import { Button } from "../button/button";

const Footer = () => {
	const currentYear = new Date().getFullYear();

	const socials = [
		{
			name: "facebook",
			link: "https://www.facebook.com/share/1FBiqfBDDG/",
			icon: facebook,
		},
		{
			name: "twitter",
			link: "https://x.com/convolly",
			icon: twitter,
		},
		{
			name: "linkedIn",
			link: "https://www.linkedin.com/company/convolly/",
			icon: linkedIn,
		},
		{
			name: "instagram",
			link: "https://www.instagram.com/convolly/?igsh=M2g1c2twOXBtOGYy",
			icon: instagram,
		},
	];

	return (
		<footer className="bg-gradient-to-br from-[#111827] to-[#0F172A] text-gray-300 py-12 sm:px-6 px-3">
			<div className="max-w-7xl mx-auto px-4">
				{/* Top section - improved layout */}
				<div className="flex flex-col lg:flex-row mb-10 lg:justify-between lg:items-start gap-8">
					{/* Logo section - takes more space on desktop */}
					<div className="flex-1 lg:max-w-[55%] mb-6 lg:mb-0">
						<div className="flex items-center mb-4">
							<div className="text-white">
								<img
									src={Logo}
									className="h-[48px] w-auto"
									alt="Convolly Logo"
								/>
							</div>
						</div>

						<p className="text-lg leading-relaxed mb-6 text-[#D1D5DB] max-w-lg">
							Convolly connects professionals with language tutors who
							understand their industry, helping them communicate effectively in
							a global business environment.
						</p>

						{/* Social media links with improved styling */}
						<div className="flex space-x-4 mb-6">
							{socials.map(({ name, icon, link }, index) => (
								<a
									key={index}
									href={link}
									target="_blank"
									rel="noopener noreferrer"
									className="bg-[#1F2937] hover:bg-primary hover:scale-110 h-11 w-11 flex justify-center items-center rounded-full transition-all duration-300 shadow-lg border border-gray-700 hover:border-primary"
								>
									<img src={icon} alt={`${name} icon`} className="w-5 h-5" />
								</a>
							))}
						</div>

						{/* Office address with better formatting */}
						<div className="">
							<h4 className="text-white font-semibold mb-2 text-sm uppercase tracking-wide"></h4>
							<p className="text-[#D1D5DB] text-md leading-relaxed">
								Office :12 Initial, business centre, Wilson business park,{" "}
								<br />
								Manchester united kingdom, M40 8WN
							</p>
						</div>
					</div>

					{/* Links section - positioned right on large screens */}
					<div className="lg:flex-shrink-0 lg:ml-8">
						<div className="flex flex-col sm:flex-row lg:flex-row gap-8 lg:gap-12">
							{/* Support links */}
							<div className="min-w-[140px]">
								<h3 className="text-white text-xl font-bold mb-4 relative">
									Support
									<div className="absolute bottom-0 left-0 w-8 h-0.5 bg-primary rounded-full"></div>
								</h3>
								<ul className="space-y-3">
									<li>
										<Link
											to="#"
											className="hover:text-primary transition-colors duration-200 hover:translate-x-1 transform inline-block text-[#D1D5DB]"
										>
											Help Center
										</Link>
									</li>
									<li>
										<Link
											to="#"
											className="hover:text-primary transition-colors duration-200 hover:translate-x-1 transform inline-block text-[#D1D5DB]"
										>
											Contact Us
										</Link>
									</li>
									<li>
										<Link
											to="#"
											className="hover:text-primary transition-colors duration-200 hover:translate-x-1 transform inline-block text-[#D1D5DB]"
										>
											FAQs
										</Link>
									</li>
								</ul>
							</div>

							{/* Legal links */}
							<div className="min-w-[140px]">
								<h3 className="text-white text-xl font-bold mb-4 relative">
									Legal
									<div className="absolute bottom-0 left-0 w-8 h-0.5 bg-primary rounded-full"></div>
								</h3>
								<ul className="space-y-3">
									<li>
										<Link
											to="#"
											className="hover:text-primary transition-colors duration-200 hover:translate-x-1 transform inline-block text-[#D1D5DB]"
										>
											Terms of Service
										</Link>
									</li>
									<li>
										<Link
											to="#"
											className="hover:text-primary transition-colors duration-200 hover:translate-x-1 transform inline-block text-[#D1D5DB]"
										>
											Privacy Policy
										</Link>
									</li>

									<li>
										<Link
											to="#"
											className="hover:text-primary transition-colors duration-200 hover:translate-x-1 transform inline-block text-[#D1D5DB]"
										>
											Refund Policy
										</Link>
									</li>
								</ul>
							</div>
						</div>
					</div>
				</div>

				{/* Elegant divider */}
				<div className="relative my-8">
					<hr className="border-gray-700" />
					<div className="absolute inset-0 flex justify-center">
						<div className="bg-gradient-to-r from-transparent via-primary to-transparent h-px w-32"></div>
					</div>
				</div>

				{/* Newsletter section with improved design */}
				<div className="mb-10 max-w-2xl">
					<p className="text-[#D1D5DB] mb-4 text-sm">
						Subscribe to Our Newsletter
					</p>
					<div className="flex flex-col sm:flex-row gap-3">
						<div className="flex-1">
							<input
								type="email"
								placeholder="Enter your email address"
								className="bg-[#111827] h-[50px] text-white px-4 py-3 rounded-lg w-full text-sm outline-none border border-gray-600 focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all duration-200"
							/>
						</div>
						<Button className="px-6 py-3 h-[50px] text-white bg-primary hover:bg-primary/90 rounded-lg transition-all duration-200 font-semibold shadow-lg hover:shadow-xl transform hover:scale-[1.02] whitespace-nowrap">
							Subscribe
						</Button>
					</div>
				</div>

				{/* Copyright with improved styling */}
				<div className="text-center pt-6 border-gray-700">
					<p className="text-[#9CA3AF] text-sm">
						© {currentYear}{" "}
						<span className="text-primary font-semibold">Convolly</span>. All
						rights reserved.
					</p>
				</div>
			</div>
		</footer>
	);
};

export default Footer;
