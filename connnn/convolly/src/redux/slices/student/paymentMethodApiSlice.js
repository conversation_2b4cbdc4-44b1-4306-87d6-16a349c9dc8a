import { generalApiSlice } from "../../apiSlice";

const paymentMethodApiSlice = generalApiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Add a new payment method
    addPaymentMethod: builder.mutation({
      query: (body) => ({
        url: `/api/payment-methods`,
        method: "POST",
        body,
      }),
      invalidatesTags: ["PaymentMethods"],
    }),

    // Get user's payment methods
    getPaymentMethods: builder.query({
      query: () => ({
        url: `/api/payment-methods`,
        method: "GET",
      }),
      providesTags: ["PaymentMethods"],
    }),

    // Set default payment method
    setDefaultPaymentMethod: builder.mutation({
      query: (paymentMethodId) => ({
        url: `/api/payment-methods/${paymentMethodId}/default`,
        method: "PATCH",
      }),
      invalidatesTags: ["PaymentMethods"],
    }),

    // Delete payment method
    deletePaymentMethod: builder.mutation({
      query: (paymentMethodId) => ({
        url: `/api/payment-methods/${paymentMethodId}`,
        method: "DELETE",
      }),
      invalidatesTags: ["PaymentMethods"],
    }),
  }),

  overrideExisting: false,
});

export const {
  useAddPaymentMethodMutation,
  useGetPaymentMethodsQuery,
  useSetDefaultPaymentMethodMutation,
  useDeletePaymentMethodMutation,
} = paymentMethodApiSlice;
