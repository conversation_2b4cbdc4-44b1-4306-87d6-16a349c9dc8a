import { StrictMode, Suspense } from "react";
import { createRoot } from "react-dom/client";
import "./index.css";
import { store } from "./redux/store.js";
import { Provider } from "react-redux";
import { RouterProvider } from "react-router-dom";
import { loadStripe } from "@stripe/stripe-js";
import { ToastProvider } from "./context/toastContext/toastContext";
import { Elements } from "@stripe/react-stripe-js";
import { QueryClient, QueryClientProvider } from "react-query";
import { router } from "./_config/inAppUrl";

// Initialize Stripe with your publishable key
const stripePromise = loadStripe(
  "pk_live_51RWDv2BuUzltG5tvKHFIpQW0QA2QQyW3ZEnSJLyFXXkY3yj2Lnq863SMR1GdPZmA5ZHeyRvAmsdNbmK1sc84RwMY00J9JsQ0pu"
);

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
    },
  },
});

createRoot(document.getElementById("root")).render(
  <StrictMode>
    <Provider store={store}>
      <ToastProvider>
        <Elements stripe={stripePromise}>
          <QueryClientProvider client={queryClient}>
            <RouterProvider router={router} />
          </QueryClientProvider>
        </Elements>
      </ToastProvider>
    </Provider>
  </StrictMode>
);
